#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一聊天API

集成AI聊天和智能体功能，提供统一的聊天接口
"""
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
from backend.app.iot.schema.agent import AgentConfig
from backend.common.response.response_schema import ResponseSchemaModel, response_base
from fastapi import Request
from backend.database.db import CurrentSession


router = APIRouter()


@router.get("/health", summary="智能体服务健康检查")
async def agent_health_check():
    """
    智能体服务健康检查端点

    不使用大模型生成，仅检查服务基础状态
    """
    try:
        # 检查智能体服务是否可以初始化
        agent_service = LangGraphAgentService()

        # 简单的服务状态检查
        health_status = {
            "status": "healthy",
            "service": "agent",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "langgraph_service": "available",
                "agent_tools": "loaded"
            }
        }

        return ResponseSchemaModel(
            code=200,
            msg="智能体服务健康",
            data=health_status
        )

    except Exception as e:
        logger.error(f"智能体服务健康检查失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"智能体服务不可用: {str(e)}"
        )


class UnifiedChatRequest(BaseModel):
    """统一聊天请求"""
    message: str = Field(..., description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID")
    mode: str = Field("agent", description="聊天模式: 固定使用智能体模式")
    model: Optional[str] = Field("Qwen3-32B-AWQ", description="使用的AI模型")
    temperature: Optional[float] = Field(0.7, description="生成温度")
    max_tokens: Optional[int] = Field(None, description="最大token数量")
    stream: bool = Field(False, description="是否使用流式响应")
    agent_config: Optional[AgentConfig] = Field(None, description="智能体配置")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")


class UnifiedChatResponse(BaseModel):
    """统一聊天响应"""
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    content: str = Field(..., description="回复内容")
    mode_used: str = Field(..., description="实际使用的模式")
    model_used: str = Field(..., description="使用的模型")
    tool_calls: List[Dict[str, Any]] = Field(default_factory=list, description="工具调用信息")
    execution_summary: Optional[str] = Field(None, description="执行摘要")
    response_time: float = Field(..., description="响应时间")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token使用情况")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


@router.post("/chat", summary="统一聊天接口", response_model=ResponseSchemaModel[UnifiedChatResponse])
async def unified_chat(
    chat_request: UnifiedChatRequest,
    request: Request,
    db: CurrentSession
):
    """
    统一聊天接口
    
    根据用户消息内容和配置，自动选择使用AI聊天或智能体模式
    """
    try:
        start_time = datetime.now()
        
        # 获取当前用户
        current_user = request.user

        # 生成会话ID和消息ID
        session_id = chat_request.session_id or str(uuid.uuid4())
        message_id = str(uuid.uuid4())

        logger.info(f"用户 {current_user.id} 发起统一聊天: {chat_request.message[:100]}... (模式: {chat_request.mode})")
        
        # 初始化服务
        langgraph_agent_service = LangGraphAgentService()

        # 统一使用智能体模式
        mode_used = "agent"

        # 使用LangGraph智能体模式（非流式）
        content, tool_calls, execution_summary, model_used = await _execute_langgraph_non_stream(
            chat_request, session_id, current_user.id, langgraph_agent_service
        )

        token_usage = None  # 智能体模式暂不统计token使用
        
        # 计算响应时间
        response_time = (datetime.now() - start_time).total_seconds()
        
        # 构建响应
        response = UnifiedChatResponse(
            session_id=session_id,
            message_id=message_id,
            content=content,
            mode_used=mode_used,
            model_used=model_used or request.model,
            tool_calls=tool_calls,
            execution_summary=execution_summary,
            response_time=response_time,
            token_usage=token_usage
        )
        
        return response_base.success(data=response)
        
    except Exception as e:
        logger.error(f"统一聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )




@router.post("/chat/stream", summary="统一流式聊天接口")
async def unified_chat_stream(
    chat_request: UnifiedChatRequest,
    request: Request,
    db: CurrentSession
):
    """
    统一流式聊天接口
    
    支持实时流式响应
    """
    try:
        # 获取当前用户
        current_user = request.user

        logger.info(f"用户 {current_user.id} 发起统一流式聊天")

        async def generate_stream():
            try:
                import json

                # 生成会话ID
                session_id = chat_request.session_id or str(uuid.uuid4())

                # 发送开始事件
                yield f"data: {json.dumps({'event': 'start', 'session_id': session_id}, ensure_ascii=False)}\n\n"

                # 添加小延迟让用户看到开始状态
                import asyncio
                await asyncio.sleep(0.5)

                # 统一使用智能体模式
                yield f"data: {json.dumps({'event': 'message', 'content': '正在使用智能体处理您的请求...'}, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.5)

                # 使用流式执行智能体
                agent_service = LangGraphAgentService()

                # 流式执行智能体对话
                has_streamed_content = False  # 标记是否有流式内容
                execution_start_time = datetime.now()
                sent_thinking_message = False  # 避免重复发送思考消息
                fallback_content = ""  # 用于非流式模式的备用内容

                try:
                    # 尝试使用流式执行 - 支持新的事件格式
                    async for stream_event in agent_service.stream_conversation(
                        message=chat_request.message,
                        session_id=session_id,
                        user_id=current_user.id,
                        config=chat_request.agent_config
                    ):
                        event_type = stream_event.get("type")

                        if event_type == "delta":
                            # token 增量 - 根据内容类型分别处理
                            delta_text = stream_event.get("content", "")
                            content_type = stream_event.get("content_type", "response")
                            node = stream_event.get("node", "unknown")

                            if delta_text:
                                has_streamed_content = True

                                # 根据内容类型发送不同的事件
                                if content_type == "thinking":
                                    yield f"data: {json.dumps({'event': 'thinking', 'content': delta_text, 'node': node}, ensure_ascii=False)}\n\n"
                                elif content_type == "tool_execution":
                                    yield f"data: {json.dumps({'event': 'tool_execution', 'content': delta_text, 'node': node}, ensure_ascii=False)}\n\n"
                                else:
                                    yield f"data: {json.dumps({'event': 'delta', 'content': delta_text, 'node': node}, ensure_ascii=False)}\n\n"
                            continue

                        elif event_type == "node_start":
                            # 节点开始事件
                            node = stream_event.get("node", "unknown")
                            yield f"data: {json.dumps({'event': 'node_start', 'node': node}, ensure_ascii=False)}\n\n"

                        elif event_type == "node_complete":
                            # 节点完成事件
                            node = stream_event.get("node", "unknown")
                            yield f"data: {json.dumps({'event': 'node_complete', 'node': node}, ensure_ascii=False)}\n\n"

                        elif event_type == "tool_call":
                            # 工具调用事件 - 使用新的格式
                            tool_name = stream_event.get("tool_name", "unknown")
                            tool_args = stream_event.get("tool_args", {})
                            tool_result = stream_event.get("tool_result", "")
                            node = stream_event.get("node", "unknown")

                            # 构建工具调用信息
                            tool_call_info = {
                                "tool_name": tool_name,
                                "node": node,
                                "arguments": tool_args,
                                "result": tool_result,
                                "success": True,
                                "execution_time": 0.1,
                                "error_message": None
                            }

                            yield f"data: {json.dumps({'event': 'tool_call', 'data': tool_call_info}, ensure_ascii=False)}\n\n"

                        elif event_type == "completion":
                            # 执行完成
                            break

                        elif event_type == "error":
                            # 错误事件
                            error_msg = stream_event.get("error", "未知错误")
                            yield f"data: {json.dumps({'event': 'error', 'error': error_msg}, ensure_ascii=False)}\n\n"
                            break

                except Exception as stream_error:
                    logger.warning(f"流式执行失败，回退到非流式模式: {stream_error}")
                    has_streamed_content = False

                # 如果流式执行没有获取到内容，使用非流式方式
                if not has_streamed_content:
                    logger.info("使用非流式方式获取智能体响应")
                    yield f"data: {json.dumps({'event': 'message', 'content': '智能体正在处理您的请求...'}, ensure_ascii=False)}\n\n"

                    # 使用重构后的流式LangGraph模式
                    async for sse_data in _execute_langgraph_stream(
                        chat_request, session_id, current_user.id, agent_service
                    ):
                        yield sse_data
                        has_streamed_content = True

                # 计算执行时间
                execution_time = (datetime.now() - execution_start_time).total_seconds()
                execution_summary = f"LangGraph智能体执行成功，耗时 {execution_time:.2f}s"

                # 发送结束事件
                yield f"data: {json.dumps({'event': 'end', 'summary': execution_summary}, ensure_ascii=False)}\n\n"

            except Exception as e:
                logger.error(f"流式聊天异常: {e}")
                yield f"data: {json.dumps({'event': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )

    except Exception as e:
        logger.error(f"统一流式聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )


async def _execute_langgraph_non_stream(
    request: UnifiedChatRequest,
    session_id: str,
    user_id: int,
    langgraph_agent_service: LangGraphAgentService
):
    """
    执行LangGraph智能体模式（非流式）

    Args:
        request: 聊天请求
        session_id: 会话ID
        user_id: 用户ID
        langgraph_agent_service: LangGraph智能体服务

    Returns:
        tuple: (content, tool_calls, execution_summary, model_used)
    """
    try:
        # 使用非流式执行
        result = await langgraph_agent_service.execute_conversation(
            message=request.message,
            session_id=session_id,
            user_id=user_id,
            config=request.agent_config
        )

        return (
            result.response,
            result.tool_calls,
            result.execution_summary,
            request.model or "Qwen3-32B-AWQ"  # AgentExecutionResult没有model_used字段
        )

    except Exception as e:
        logger.error(f"LangGraph智能体模式执行异常: {e}")
        return (
            f"智能体执行异常: {str(e)}",
            [],
            f"执行失败: {str(e)}",
            request.model or "Qwen3-32B-AWQ"
        )


async def _execute_langgraph_stream(
    request: UnifiedChatRequest,
    session_id: str,
    user_id: int,
    langgraph_agent_service: LangGraphAgentService
):
    """
    执行LangGraph智能体模式（流式）

    Args:
        request: 聊天请求
        session_id: 会话ID
        user_id: 用户ID
        langgraph_agent_service: LangGraph智能体服务

    Yields:
        str: SSE格式的流式响应数据
    """
    try:
        import json

        # 使用流式处理
        async for event in langgraph_agent_service.stream_conversation(
            message=request.message,
            session_id=session_id,
            user_id=user_id,
            config=request.agent_config,
            force_token_streaming=True  # 强制使用逐token流式输出
        ):
            # 直接yield每个事件，转换为SSE格式
            yield f"data: {json.dumps(event, ensure_ascii=False)}\n\n"

        # 发送完成标识
        yield "data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"LangGraph智能体流式模式执行异常: {e}")
        error_event = {
            "type": "error",
            "content": f"LangGraph智能体执行异常: {str(e)}",
            "error": str(e)
        }
        yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"
        yield "data: [DONE]\n\n"
